@import "primeicons/primeicons.css";

// Ensure light theme for PrimeNG components
html, body {
  color-scheme: light;
}

// Глобальные стили для всех PrimeNG таблиц
.p-datatable .p-datatable-thead > tr > th {
  background-color: #f9fafb !important;
  color: #111827 !important;
  font-weight: 600 !important;
  border-bottom: 2px solid #e5e7eb !important;
}

// Темная тема для PrimeNG таблиц
.dark .p-datatable .p-datatable-thead > tr > th {
  background-color: #374151 !important;
  color: #f9fafb !important;
  border-bottom-color: #4b5563 !important;
}

// Force light theme for PrimeNG components
:root {
  --p-surface-0: #ffffff;
  --p-surface-50: #f8fafc;
  --p-surface-100: #f1f5f9;
  --p-surface-200: #e2e8f0;
  --p-surface-300: #cbd5e1;
  --p-surface-400: #94a3b8;
  --p-surface-500: #64748b;
  --p-surface-600: #475569;
  --p-surface-700: #334155;
  --p-surface-800: #1e293b;
  --p-surface-900: #0f172a;
  --p-surface-950: #020617;
  --p-content-color: #1e293b;
  --p-text-color: #1e293b;
}

.input-hidden {
  display: none !important;
}

// Remove gray background from PrimeNG select components
.p-select {
  background: transparent !important;
}

.p-select:hover {
  background: transparent !important;
}

.p-select-option.p-select-option-selected.p-focus {
  background: transparent !important;
}

.p-select-option.p-select-option-selected {
  background: transparent !important;
}

.p-select-option:hover {
  background: transparent !important;
}

// More specific selectors to override component styles
[class*="ng-"] .p-select {
  background: transparent !important;
}

[class*="ng-"] .p-select:hover {
  background: transparent !important;
}

[class*="ng-"] .p-select-option.p-select-option-selected.p-focus {
  background: transparent !important;
}

[class*="ng-"] .p-select-option.p-select-option-selected {
  background: transparent !important;
}

[class*="ng-"] .p-select-option:hover {
  background: transparent !important;
}

// html {
//   &::-webkit-scrollbar {
//     width: 10px;
//   }

//   &::-webkit-scrollbar-track {
//     background: #fff;
//   }

//   &::-webkit-scrollbar-thumb {
//     background: #9f9f9f;
//     border-radius: 5px;
//   }

//   &::-webkit-scrollbar-thumb:hover {
//     background: red;
//   }

// }